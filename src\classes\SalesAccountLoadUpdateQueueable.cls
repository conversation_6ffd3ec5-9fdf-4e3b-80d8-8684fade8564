/*
 * Description: Queueable Apex class to handle large Sales Account Load updates asynchronously
 * Purpose: Prevents DML limit exceptions when updating large volumes of Sales_Account_Load__c records
 * @Test: SalesAccountLoadUpdateQueueableTest
 */
public class SalesAccountLoadUpdateQueueable implements Queueable {

    private List<Sales_Account_Load__c> recordsToUpdate;
    private static final Integer MAX_BATCH_SIZE = 9000; // Safe batch size with buffer for other operations
    private static final Integer MIN_DML_BUFFER = 100; // Minimum DML rows to keep available for other operations

    public SalesAccountLoadUpdateQueueable(List<Sales_Account_Load__c> recordsToUpdate) {
        this.recordsToUpdate = recordsToUpdate;
    }

    /**
     * Execute method - processes the records in batches to respect DML limits
     */
    public void execute(QueueableContext context) {
        try {
            if (recordsToUpdate == null || recordsToUpdate.isEmpty()) {
                ApplicationLogUtility.logInfo(
                    SalesAccountLoadUpdateQueueable.class.getName(),
                    'execute',
                    'No records to process - recordsToUpdate is null or empty',
                    '',
                    0
                );
                ApplicationLogUtility.commitLog();
                return;
            }

            // Calculate safe batch size based on remaining DML capacity
            Integer currentDmlRows = Limits.getDmlRows();
            Integer maxDmlRows = Limits.getLimitDmlRows();
            Integer remainingDmlRows = maxDmlRows - currentDmlRows;

            // Ensure we leave a buffer for other operations and don't exceed our max batch size
            Integer safeBatchSize = Math.min(
                Math.min(MAX_BATCH_SIZE, remainingDmlRows - MIN_DML_BUFFER),
                recordsToUpdate.size()
            );

            ApplicationLogUtility.logInfo(
                SalesAccountLoadUpdateQueueable.class.getName(),
                'execute',
                'DML Analysis - Current: ' + currentDmlRows +
                ', Limit: ' + maxDmlRows +
                ', Remaining: ' + remainingDmlRows +
                ', Safe Batch Size: ' + safeBatchSize +
                ', Total Records: ' + recordsToUpdate.size(),
                '',
                0
            );
            ApplicationLogUtility.commitLog();

            // If no safe capacity available, requeue all records for next transaction
            if (safeBatchSize <= 0) {
                ApplicationLogUtility.logInfo(
                    SalesAccountLoadUpdateQueueable.class.getName(),
                    'execute',
                    'No DML capacity available. Requeuing all ' + recordsToUpdate.size() + ' records for next transaction',
                    '',
                    0
                );
                ApplicationLogUtility.commitLog();

                System.enqueueJob(new SalesAccountLoadUpdateQueueable(recordsToUpdate));
                return;
            }

            List<Sales_Account_Load__c> currentBatch = new List<Sales_Account_Load__c>();
            List<Sales_Account_Load__c> remainingRecords = new List<Sales_Account_Load__c>();

            // Split records into current batch and remaining records
            for (Integer i = 0; i < recordsToUpdate.size(); i++) {
                if (i < safeBatchSize) {
                    currentBatch.add(recordsToUpdate[i]);
                } else {
                    remainingRecords.add(recordsToUpdate[i]);
                }
            }

            // Update the current batch
            if (!currentBatch.isEmpty()) {
                // Double-check DML limits before performing update
                if (Limits.getDmlRows() + currentBatch.size() > Limits.getLimitDmlRows() - MIN_DML_BUFFER) {
                    ApplicationLogUtility.logError(
                        SalesAccountLoadUpdateQueueable.class.getName(),
                        'execute',
                        new DmlException('DML limit would be exceeded'),
                        'DML capacity changed during execution. Requeuing all records.',
                        '',
                        0
                    );
                    ApplicationLogUtility.commitLog();

                    // Requeue all records for next transaction
                    System.enqueueJob(new SalesAccountLoadUpdateQueueable(recordsToUpdate));
                    return;
                }

                Database.SaveResult[] results = Database.update(currentBatch, false);

                // Process results and log any failures
                List<String> errors = new List<String>();
                Integer successCount = 0;

                for (Integer i = 0; i < results.size(); i++) {
                    if (results[i].isSuccess()) {
                        successCount++;
                    } else {
                        for (Database.Error error : results[i].getErrors()) {
                            errors.add('Record ID: ' + currentBatch[i].Id + ', Error: ' + error.getMessage());
                        }
                    }
                }

                // Log any failures
                if (!errors.isEmpty()) {
                    ApplicationLogUtility.logError(
                        SalesAccountLoadUpdateQueueable.class.getName(),
                        'execute',
                        new DmlException(String.join(errors, '; ')),
                        'Failed to update ' + errors.size() + ' out of ' + currentBatch.size() + ' Sales Account Load records', 
                        '',
                        0
                    );
                    ApplicationLogUtility.commitLog();
                }

                // Log successful processing
                ApplicationLogUtility.logInfo(
                    SalesAccountLoadUpdateQueueable.class.getName(),
                    'execute',
                    'Successfully processed ' + successCount + ' out of ' + currentBatch.size() + ' Sales Account Load records',
                    '',
                    0
                );
                ApplicationLogUtility.commitLog();
            }

            // If there are remaining records, queue another job to process them
            if (!remainingRecords.isEmpty()) {
                System.enqueueJob(new SalesAccountLoadUpdateQueueable(remainingRecords));

                ApplicationLogUtility.logInfo(
                    SalesAccountLoadUpdateQueueable.class.getName(),
                    'execute',
                    'Queued additional job to process remaining ' + remainingRecords.size() + ' Sales Account Load records',
                    '',
                    0
                );
                ApplicationLogUtility.commitLog();
            } else {
                ApplicationLogUtility.logInfo(
                    SalesAccountLoadUpdateQueueable.class.getName(),
                    'execute',
                    'All Sales Account Load records have been processed successfully',
                    '',
                    0
                );
                ApplicationLogUtility.commitLog();
            }
        } catch (Exception e) {
            // Log the error
            ApplicationLogUtility.logError(
                SalesAccountLoadUpdateQueueable.class.getName(), 
                'execute',
                e,
                'Error processing Sales Account Load updates: ' + e.getMessage(), 
                '',
                0
            );
            ApplicationLogUtility.commitLog();

            // For certain recoverable errors, implement retry logic
            if (isRecoverableError(e)) {
                ApplicationLogUtility.logInfo(
                    SalesAccountLoadUpdateQueueable.class.getName(), 
                    'execute',
                    'Recoverable error detected. Requeuing ' + recordsToUpdate.size() + ' records for retry',
                    '',
                    0
                );
                ApplicationLogUtility.commitLog();

                System.enqueueJob(new SalesAccountLoadUpdateQueueable(recordsToUpdate));
            }
        }
    }

    /**
     * Helper method to determine if an error is recoverable
     */
    private Boolean isRecoverableError(Exception e) {
        String errorMessage = e.getMessage().toLowerCase();

        // Define recoverable error patterns
        List<String> recoverableErrors = new List<String>{
            'unable_to_lock_row',
            'timeout',
            'temporarily unavailable',
            'system overloaded'
        };

        for (String recoverableError : recoverableErrors) {
            if (errorMessage.contains(recoverableError)) {
                return true;
            }
        }

        return false;
    }
}