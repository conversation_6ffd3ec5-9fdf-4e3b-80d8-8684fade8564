/* 
 * Description: Trigger Helper class for Commission_Payee__c object
 * TestClass: CommissionPayeeTriggerHelperTest
*/
public without sharing class CommissionPayeeTriggerHelper {
	public static void updateSalesAccountOnCommissionPayeeChange(List<Commission_Payee__c> newList, Map<Id, Commission_Payee__c> oldMap) {

		Set<Id> commissionPayeeIdSet = new Set<Id>();
		List<Sales_Account__c> commissionPayeeSalesAccountList = new List<Sales_Account__c>();
		Map<Id, List<Sales_Account__c>> associatedSalesAccountMap = new Map<Id, List<Sales_Account__c>>();
		List<Sales_Account__c> toBeUpdatedSalesAccountList = new List <Sales_Account__c> ();

		//get list of Commission Payee with updated Advised Flags
		//only add to list records that has Advised Flag set to True
		//Advised Flags that has been set to false will not update anything
		for (Commission_Payee__c newCP : newList) {
			Commission_Payee__c oldCP = oldMap.get(newCP.Id);
			if (oldCP.Advised_Flag__c != newCP.Advised_Flag__c &&
					newCP.Advised_Flag__c != true) {
				commissionPayeeIdSet.add(newCP.Id);
			}
		}
		/*
		 * Expected Output:
		 * 		Commission Payee 1
		 * 		Commission Payee 2
		 * 		Commission Payee 3
		 * 		Commission Payee 4
		 * */

		//check if Commission Payee list is not empty
		//get all related Sales Account from the results of updated Commission Payee list
		if (!commissionPayeeIdSet.isEmpty()) {
			commissionPayeeSalesAccountList = [SELECT Id, Commission_Payee__c, Advised_Flag__c FROM Sales_Account__c WHERE Commission_Payee__c IN:commissionPayeeIdSet];

			/*
			 * Expected Output:
			 * 		Sales Account 1 (Commission Payee 1)
			 * 		Sales Account 2 (Commission Payee 1)
			 * 		Sales Account 3 (Commission Payee 2)
			 * 		Sales Account 4 (Commission Payee 3)
			 * 		Sales Account 5 (Commission Payee 4)
			 * */

			//assign Commission Payee to a map with a list of all related Service Accounts
			for (Sales_Account__c sa : commissionPayeeSalesAccountList) {
				//create a map if it doesn't exist yet
				if (!associatedSalesAccountMap.containsKey(sa.Commission_Payee__c)) {
					associatedSalesAccountMap.put(sa.Commission_Payee__c, new List <Sales_Account__c>{
							sa
					});
				}
				/*
				 * Expected Output:
				 * 		<Commission Payee 1, {Sales Account 1}>
				 **/

				//if map already exist, just add SA to existing list of map
				else if (associatedSalesAccountMap.containsKey(sa.Commission_Payee__c)) {
					associatedSalesAccountMap.get(sa.Commission_Payee__c).add(sa);
				}
				/*
				 * Expected Output:
				 * 		<Commission Payee 1, {Sales Account 1, Sales Account 2}>
				 **/
			}

			//loop again the list of updated Commission Payee
			for (Commission_Payee__c newCP : newList) {
				//set value of Advised Flag of Sales Account with the value of the Commission Payee Advised Flag
				//add changes to a list
				//Check if there is existing associated sales account to avoid unhandled APEX error
				//Eugene Ray Perfecio (Accenture) July 22, 2019 SFP-8479
				if (associatedSalesAccountMap.get(newCP.Id) != NULL) {
					for (Sales_Account__c sa : associatedSalesAccountMap.get(newCP.Id)) {
						sa.Advised_Flag__c = newCP.Advised_Flag__c;
						toBeUpdatedSalesAccountList.add(sa);
					}

				}
			}

			//update SalesAccount records using the list
			update toBeUpdatedSalesAccountList;
		}
	}
    
    /*
    *	<AUTHOR> J.Mendoza (Accenture) 29/Jul/20
    *   @Description: A method that automatically sets SAL's integration status = Fixed, when commission payee integration id == SAL's calculated payee id
    *   @Story      : SFP-8793
    *   @Modified   : Updated to handle DML limits using Queueable Apex for large data volumes
	*/
    public static void updateSalesAccLoadToFixed(List<Commission_Payee__c> newList, Map<Id, Commission_Payee__c> oldMap) {
		try{
			Set<String> integrationIds = new Set<String>();
			List<Sales_Account_Load__c> salesAccountLoadToUpdateList = new List<Sales_Account_Load__c>();

			for(Commission_Payee__c cp : newList){
				if(Trigger.isUpdate){
					if(cp.Integration_Id__c != oldMap.get(cp.Id).Integration_Id__c){
						if(oldMap.get(cp.Id).Integration_Id__c!='' && oldMap.get(cp.Id).Integration_Id__c!=null){
							integrationIds.add(oldMap.get(cp.Id).Integration_Id__c);
						}
						if(cp.Integration_Id__c!='' && cp.Integration_Id__c!=null){
							integrationIds.add(cp.Integration_Id__c);
						}
					} else if(cp.Integration_Id__c!='' && cp.Integration_Id__c!=null
					   && (cp.Account__c != oldMap.get(cp.Id).Account__c
						   || cp.PayeeId__c != oldMap.get(cp.Id).PayeeId__c
						   || cp.Status__c != oldMap.get(cp.Id).Status__c
						   || cp.Source_System__c != oldMap.get(cp.Id).Source_System__c))
					{
						integrationIds.add(cp.Integration_Id__c);
					}
				} else if(Trigger.isInsert){
					if(cp.Integration_Id__c!='' && cp.Integration_Id__c!=null){
						integrationIds.add(cp.Integration_Id__c);
					}
				}
			}
			// get SAL where Calculated_Payee_Id__c == CommissionPayee.IntegrationId then update SAL Status to 'Fixed'
			if(!integrationIds.isEmpty()){
				for(Sales_Account_Load__c sal : [SELECT Id, Status__c, Calculated_Payee_Id__c FROM Sales_Account_Load__c WHERE Calculated_Payee_Id__c IN: integrationIds AND Integration_Status__c != :ConstantsHierarchy.SAL_INTEGRATION_STATUS_FIXED]){
					sal.Integration_Status__c = ConstantsHierarchy.SAL_INTEGRATION_STATUS_FIXED;
					salesAccountLoadToUpdateList.add(sal);
				}
			}

			// Check DML limits and handle large data volumes
			if(!salesAccountLoadToUpdateList.isEmpty()){
				Integer currentDMLRows = Limits.getDMLRows();
				Integer availableDMLRows = Limits.getLimitDMLRows() - currentDMLRows;

				// If we're approaching DML limits (leaving 1000 row buffer for other operations), use async processing
				if(salesAccountLoadToUpdateList.size() > (availableDMLRows - 1000) || salesAccountLoadToUpdateList.size() > 9000) {
					// Queue the update for asynchronous processing to avoid DML limits
					System.enqueueJob(new SalesAccountLoadUpdateQueueable(salesAccountLoadToUpdateList));

					// Log the async processing for monitoring
					ApplicationLogUtility.logInfo(CommissionPayeeTriggerHelper.class.getName(), 'updateSalesAccLoadToFixed',
						'Large data volume detected (' + salesAccountLoadToUpdateList.size() + ' records). Processing asynchronously to avoid DML limits.', '', 0);
					ApplicationLogUtility.commitLog();
				} else {
					// Safe to update synchronously
					update salesAccountLoadToUpdateList;
				}
			}
		} catch (Exception e) {
			ApplicationLogUtility.logError(CommissionPayeeTriggerHelper.class.getName(), 'updateSalesAccLoadToFixed', e, e.getMessage(), '', 0); ApplicationLogUtility.commitLog();
		}
    }
    
    /*
    *	<AUTHOR> J.Mendoza (Accenture) 29/Jul/20
    *   @Description: A method that checks to ensure there are no sales accounts where the Calculated Payee Id matches the Integration Id
    *   @Story      : SFP-8793
	*/
    public static void checkCalcPayeeEqualsIntegId(List<Commission_Payee__c> oldList) {	
		try{
			Set<String> integIds = new Set<String>();
            Set<String> calcPayeeIds = new Set<String>();

            for(Commission_Payee__c cp : oldList){
                if(cp.Integration_Id__c!=null){
                    integIds.add(cp.Integration_Id__c);
                }
            }

			for(Sales_Account_Load__c sal : [SELECT Id, Status__c, Calculated_Payee_Id__c FROM Sales_Account_Load__c WHERE Calculated_Payee_Id__c IN: integIds]){
				calcPayeeIds.add(sal.Calculated_Payee_Id__c);
			}
	
			for(Commission_Payee__c cp : oldList){
				if(!calcPayeeIds.isEmpty() && calcPayeeIds.contains(cp.Integration_Id__c)){
					cp.addError(Label.Existing_Sales_Account_Deletion_Error_Message);
				}
			}
		} catch (Exception e) {
			ApplicationLogUtility.logError(CommissionPayeeTriggerHelper.class.getName(), 'checkCalcPayeeEqualsIntegId', e, e.getMessage(), '', 0); ApplicationLogUtility.commitLog();
		}
    }
}