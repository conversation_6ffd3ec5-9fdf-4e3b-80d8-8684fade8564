/*
 * Description: Test class for SalesAccountLoadUpdateQueueable
 * Purpose: Ensures the Queueable Apex class handles large data volumes correctly
 */
@isTest
public class SalesAccountLoadUpdateQueueableTest {
    @testSetup
    static void setupTestData() {
        // Create test Sales Account Load records
        List<Sales_Account_Load__c> testRecords = new List<Sales_Account_Load__c>();

        for (Integer i = 0; i < 50; i++) {
            Sales_Account_Load__c sal = new Sales_Account_Load__c(
                Status__c = ConstantsHierarchy.SAL_STATUS_ACTIVE,
                Calculated_Payee_Id__c = 'TEST_PAYEE_' + i,
                Start_Date__c = Date.today()
            );
            testRecords.add(sal);
        }

        insert testRecords;
    }

    @isTest
    static void queueableExecution_ShouldLoadRecords() {
        // Given
        List<Sales_Account_Load__c> testRecords = [SELECT Id, Status__c, Integration_Status__c FROM Sales_Account_Load__c LIMIT 50];

        // Update the records to simulate the trigger scenario
        for (Sales_Account_Load__c sal : testRecords) {
            sal.Integration_Status__c = ConstantsHierarchy.SAL_INTEGRATION_STATUS_FIXED;
        }

        // When
        Test.startTest();

        SalesAccountLoadUpdateQueueable queueable = new SalesAccountLoadUpdateQueueable(testRecords);
        System.enqueueJob(queueable);

        Test.stopTest();

        // Then

        List<Sales_Account_Load__c> updatedRecords = [SELECT Id, Integration_Status__c FROM Sales_Account_Load__c];

        System.assertEquals(50, updatedRecords.size(), 'All test records should be present');

        for (Sales_Account_Load__c sal : updatedRecords) {
            System.assertEquals(ConstantsHierarchy.SAL_INTEGRATION_STATUS_FIXED, sal.Integration_Status__c, 
                'Sales Account Load record should have been updated to Fixed status');
        }
    }

    @isTest
    static void queueable_ShouldWork_WithEmptyListAndNulls() {
        // When
        Test.startTest();

        SalesAccountLoadUpdateQueueable queueable_Empty = new SalesAccountLoadUpdateQueueable(new List<Sales_Account_Load__c>());
        System.enqueueJob(queueable_Empty);

        SalesAccountLoadUpdateQueueable queueable_Null = new SalesAccountLoadUpdateQueueable(null);
        System.enqueueJob(queueable_Null);

        Test.stopTest();

        // Then
        System.assert(true, 'Queueable should handle empty and null list gracefully');
    }
}