/** @TestClass:   NewCommsLogControllerTest
*/


public with sharing class newCommsLogController {
	/**
	* <PERSON> Inocente 6-26-19 edit: added else if for RecordtypeId CCS assist sfp 11123
	**/
	@AuraEnabled
	public static string getCaseLocation(string recordTypeId) {
 		if (recordTypeId == ConstantsCases.CASE_RT_CONTACT_CENTRE_SERVICE_BANK) {
			return 'Bank';
		} else if (recordTypeId == ConstantsCases.CASE_RT_CONTACT_CENTRE_SERVICE_ASSIST) {
			return 'Assist';
		} else {
			return 'AU';
		}
	}

	@AuraEnabled
	public static List<CCS_Description_Template__mdt> getDescriptionTemplates() {
		return [
				select Id, Category__c, Sub_Category__c, Classification__c, Default_Description__c
				from CCS_Description_Template__mdt
		];
	}
	/**
	* Vincent Inocente 6-26-19 edit: added category for CCS assist sfp 11123
	
	**/
    
    /*
     * SFP-24675 - <PERSON>, Commented Out
	@AuraEnabled
	public static map<Id, List<string>> categoryOptions {
        get {
            Map<Id, List<string>> categoryMap = new Map<Id, List<string>>();
            
            List<CCS_Layout_New__mdt> categoriesList = [
                SELECT PIcklist_Value__c, Record_Type__c
                FROM CCS_Layout_New__mdt
                WHERE PIcklist_Type__c='Category'
            ];
            
            for (CCS_Layout_New__mdt cat: categoriesList) {
                categoryMap.put(Schema.SObjectType.Case.getRecordTypeInfosByName().get(cat.Record_Type__c).getRecordTypeId(), cat.PIcklist_Value__c.split(','));
            }
            
            catMap.put(ConstantsCases.CASE_RT_CONTACT_CENTRE_SERVICE_AU, new List<string>{
            'Retail Service'
            });
            catMap.put(ConstantsCases.CASE_RT_CONTACT_CENTRE_SERVICE_BANK, new List<string>{
            'Bank'
            });
            catMap.put(ConstantsCases.CASE_RT_CONTACT_CENTRE_SERVICE_ASSIST, new List<string>{
            'Retention', 'AMP Assist Campaigns', 'Sales'
            });         
            return categoryMap; 
        }
	}
    */

	@AuraEnabled
	public static List<string> getCategoryOptions(Id recordTypeId) {
        //return categoryOptions.get(recordTypeId);
		
        //SFP-24675 - James Tucay
        Map<Id, List<string>> metaDataMap = new Map<Id, List<string>>();
        
        List<CCS_Layout_New__mdt> metadataList = [
            SELECT Picklist_Value__c, Identifier__c
            FROM CCS_Layout_New__mdt
            WHERE Picklist_Type__c = 'Category'
        ];
        
        for (CCS_Layout_New__mdt element: metadataList) {
            metaDataMap.put(Schema.SObjectType.Case.getRecordTypeInfosByName().get(element.Identifier__c).getRecordTypeId(), (List<String>)JSON.deserialize(element.Picklist_Value__c, List<String>.class));
        }      
        
		return metaDataMap.get(recordTypeId);
	}
    
	/*
	 * SFP-24675 - James Tucay, Commented out
	@AuraEnabled
	public static map<string, List<string>> subCategoryOptions {
		get {
			map<string, List<string>> subCatMap = new map<string, List<string>>();

			subCatMap.put('Bank', new List<string>{
					'Close Account', 'Complaint', 'Forms', 'Growth', 'Maintenance / Servicing', 'Online', 'Product Enquiry', 'Retention', 'Transactions', 'Work In Progress Enquiry'
			});
			subCatMap.put('NZ AMP Sales', new List<string>{
					'General Insurance', 'Investment Other', 'KiwiSaver', 'Lifetrack', 'NZRT'
			});
			subCatMap.put('NZ Customer Service', new List<string>{
					'Campaigns', 'Contact Details Update', 'Enquiry', 'Follow Up', 'Form Request', 'Missed Payment Notice', 'Online Access', 'Outbound Call Back', 'Phone Payment', 'Phone Withdrawal', 'Policy Alteration', 'Withdrawal Form Request'
			});
			subCatMap.put('Retail Service', new List<string>{
					'Correspondence', 'Feedback', 'Payments In', 'Payments Out', 'Servicing', 'Work In Progress'
			});

			return subCatMap;
		}
	}
	*/

	@AuraEnabled
	public static List<string> getSubCategoryOptions(string category) {
		//return subCategoryOptions.get(category);
		
        //SFP-24675 - James Tucay
        Map<String, List<String>> metaDataMap = new Map<String, List<String>>();
        
        List<CCS_Layout_New__mdt> metadataList = [
            SELECT Picklist_Value__c, Identifier__c
            FROM CCS_Layout_New__mdt
            WHERE Picklist_Type__c = 'SubCategory'
        ];
        
        for (CCS_Layout_New__mdt element: metadataList) {
            metaDataMap.put(element.Identifier__c, (List<String>)JSON.deserialize(element.Picklist_Value__c, List<String>.class));
        }
        
        return metaDataMap.get(category);
	}

	@AuraEnabled
	public static map<string, List<string>> classificationOptions {
		get {
			map<string, List<string>> classMap = new map<string, List<string>>();

			classMap.put('Correspondence', new List<string>{
					'Ad Hoc Letter / Correspondence', 'Form Enquiry / Request', 'Payment Notice', 'Statement Enquiry'
			});
			classMap.put('Feedback', new List<string>{
					'Compliment', 'Projects/Events', 'Natural Disaster Relief', 'Royal Commission 2018', 'Suggestion/Improvements'
			});//hide 'AR Announcement' SFP-25414
			classMap.put('Payments In', new List<string>{
					'Ad Hoc Payment', 'Consolidation', 'Contribution', 'Missed Payment'
			});
			classMap.put('Payments Out', new List<string>{
					'Cancel', 'Claim', 'Early Release', 'EasyDraw', 'Refund', 'Under $200', 'Withdrawal'
			});
			classMap.put('Servicing', new List<string>{
					'Account Information', 'Lost Super', 'Maintenance', 'Product Information', 'Unclaimed Money'
			});
			classMap.put('Work In Progress', new List<string>{
					'Claims', 'New Business', 'Payments In', 'Payments Out', 'Servicing', 'Adviser Complaint', 'Complaint'
			});

			classMap.put('General Insurance', new List<string>{
					'Campaign', 'New Business', 'Review', 'Customer Enquiry', 'Retention', 'Endorsement'
			});
			classMap.put('KiwiSaver', new List<string>{
					'Campaign', 'New Business', 'Review', 'Switch', 'Website follow up', 'Extra FUM', 'Welcome Call'
			});
			classMap.put('Lifetrack', new List<string>{
					'New Business'
			});
			classMap.put('NZRT', new List<string>{
					'My Super retention', 'Returns', 'Extra FUM'
			});
			classMap.put('Investment Other', new List<string>{
					'New Business', 'Review', 'Returns', 'Customer Enquiry'
			});
			classMap.put('Campaigns', new List<string>{
					'Making Plans', 'Member Tax Credits', 'Other'
			});
			classMap.put('Contact Details Update', new List<string>{
					'Updated'
			});
			classMap.put('Enquiry', new List<string>{
					'Cancellation', 'Fund Performance', 'General Enquiry', 'Payment Enquiry', 'Request for Quotes'
			});
			classMap.put('Follow Up', new List<string>{
					'Work in Progress'
			});
			classMap.put('Form Request', new List<string>{
					'3rd Party Authority Form', 'Cancellation Form', 'Change of Personal Details', 'Changing Your Investments Form', 'Direct Debit Authority', 'Identity Verification Form', 'New Application Form', 'Other Form', 'Transfer Application', 'Transfer of Ownership Form'
			});
			classMap.put('Missed Payment Notice', new List<string>{
					'Credit Card Payment', 'Direct Debit', 'One Off', 'Special Billing'
			});
			classMap.put('Online Access', new List<string>{
					'My AMP'
			});
			classMap.put('Outbound Call Back', new List<string>{
					'Escalation Call Back', 'More Information Required', 'Call Back'
			});
			classMap.put('Phone Payment', new List<string>{
					'Credit Card Payment', 'Direct Debit', 'Special Billing'
			});
			classMap.put('Phone Withdrawal', new List<string>{
					'20% Withdrawal', 'Full Withdrawal', 'Partial Withdrawal'
			});
			classMap.put('Policy Alteration', new List<string>{
					'Direct Debit', 'IRD Number', 'Other', 'Payment Date', 'PIE Tax Change', 'Switch'
			});
			classMap.put('Withdrawal Form Request', new List<string>{
					'Deceased Withdrawal', 'Financial Hardship Withdrawal', 'First Home Withdrawal', 'Loan on Policy', 'Maturity', 'Other', 'Permanent Emigration', 'Retirement Withdrawal', 'Serious Illness Withdrawal', 'Surrender'
			});

			classMap.put('Close Account', new List<string>{
					'Break Cost / Payout Amount', 'Loan Discharge Request', 'Term Deposit', 'Transaction Account'
			});
			classMap.put('Complaint', new List<string>{
					'Delay / Error', 'Fees', 'Fulfilment', 'Online', 'Other', 'Policy / Rules', 'Product', 'Service', 'Royal Commission 2018'
			});
			classMap.put('Forms', new List<string>{
					'Application', 'Identification', 'Maintenance / Servicing', 'Other'
			});
			classMap.put('Growth', new List<string>{
					'Refer Adviser', 'Refer BDM', 'Refer Broker', 'Refer Planner Assist', 'Refer Direct Sales', 'Refer My AMP'
			});
			classMap.put('Maintenance / Servicing', new List<string>{
					'Access Cards / Tokens', 'Account Details', 'Adviser / Broker Details', 'Balance Enquiry', 'Change Contact Details', 'Cheque / Deposit Books', 'Electronic Comms Consent', 'Loan Fixed Rate Expiry', 'Loan Repayments', 'Statement/ Transaction Listing', 'Term Deposit Maturity / Break', 'TFN Nomination'
			});
			classMap.put('Online', new List<string>{
					'Functions', 'Login Issues', 'Navigation', 'Other', 'Password Reset', 'Registration'
			});
			classMap.put('Product Enquiry', new List<string>{
					'Home Loan', 'Term Deposit', 'Transaction / Savings Account', 'Variation to Existing Loan'
			});
			classMap.put('Retention', new List<string>{
					'Break Costs Enquiry', 'Changed Circumstances', 'Complaint', 'Credit Decision', 'Discharge Request', 'External Refinance', 'Fees', 'Fixed Rate / Int Only Expiry', 'Interest Rate', 'Loan Variation', 'Online Enquiry', 'Payout Request', 'Policy / Rules', 'Property Sale', 'Refer BDM', 'Refer Broker / Adviser', 'Service', 'Statement/ Transaction Listing', 'Valuation'
			});
			classMap.put('Transactions', new List<string>{
					'Bank Cheque', 'Funds Transfer', 'Query Transaction', 'Telegraphic Transfer / SWIFT'
			});
			classMap.put('Work In Progress Enquiry', new List<string>{
					'Account Maintenance', 'Discharge / Close', 'Documents Received', 'Loan Documentation', 'Loan Variation', 'New Loan Application', 'Settlement', 'Term Deposit Application', 'Transaction Account App', 'Valuation'
			});


			return classMap;
		}
	}

	@AuraEnabled
	public static List<string> getClassificationOptions(string subCategory) {
		//return classificationOptions.get(subCategory);

        Map<String, List<String>> metaDataMap = new Map<String, List<String>>();
        
        List<CCS_Layout_New__mdt> metadataList = [
            SELECT Picklist_Value__c, Identifier__c
            FROM CCS_Layout_New__mdt
            WHERE Picklist_Type__c = 'Classification'
        ];
        
        for (CCS_Layout_New__mdt element: metadataList) {
            metaDataMap.put(element.Identifier__c, (List<String>)JSON.deserialize(element.Picklist_Value__c, List<String>.class));
        }
        
        return metaDataMap.get(subCategory);
	}

	@AuraEnabled
	public static List<Account> getAccounts(string objType, Id recordId) {
		List<Account> accs = new List<Account>();

		if (objType == 'Account') {

			accs = [select Id, Name from Account where Id = :recordId limit 1];

		}

		else if (objType == 'Contact') {

			string conId = [select AccountId from Contact where Id = :recordId limit 1].AccountId;
			accs = [select Id, Name from Account where Id = :conId limit 1];

		}

		else if (objType == 'FinServ__FinancialAccount__c') {

			FinServ__FinancialAccount__c fa = [
					select SCV_PrimaryOwner__c, SCV_PrimaryOwner__r.Name,
							SCV_JointOwner__c, SCV_JointOwner__r.Name
					from FinServ__FinancialAccount__c
					where Id = :recordId
			];
			Account po = new Account(
					Id = fa.SCV_PrimaryOwner__c,
					Name = fa.SCV_PrimaryOwner__r.Name
			);
			accs.add(po);

			if (fa.SCV_JointOwner__c != null) {
				Account jo = new Account(
						Id = fa.SCV_JointOwner__c,
						Name = fa.SCV_JointOwner__r.Name
				);
				accs.add(jo);
			}

		} else {


		}

		UtilityClass.debug(accs);
		return accs;
	}

	//Salanap (ECRM 14780) - updated query
	@AuraEnabled
	public static List<FinServ__FinancialAccount__c> getFinancialAccounts(string objType, Id recordId) {
		List<FinServ__FinancialAccount__c> finAccs = new List<FinServ__FinancialAccount__c>();

		if (objType == 'FinServ__FinancialAccount__c') {

			finAccs = [
					select Id, Name, FinServ__FinancialAccountNumber__c, SCV_PrimaryOwner__c, SCV_JointOwner__c,
							Product__r.Name, Product_Code__c
					from FinServ__FinancialAccount__c
					where Id = :recordId
					limit 1
			];

		} else if (objType == 'Account') {

			finAccs = [
					select Id, Name, FinServ__FinancialAccountNumber__c, Product__r.Name, SCV_PrimaryOwner__c, SCV_JointOwner__c,
							Product_Code__c
					from FinServ__FinancialAccount__c
					where FinServ__PrimaryOwner__c = :recordId OR SCV_PrimaryOwner__c = :recordId
					ORDER BY Active_Flag__c DESC NULLS LAST
			];

		} else if (objType == 'Contact') {

			string conId = [select AccountId from Contact where Id = :recordId limit 1].AccountId;
			finAccs = [
					select Id, Name, FinServ__FinancialAccountNumber__c, Product__r.Name, SCV_PrimaryOwner__c, SCV_JointOwner__c, Product_Code__c
					from FinServ__FinancialAccount__c
					where FinServ__PrimaryOwner__c = :conId
					or FinServ__JointOwner__c = :conId
					or SCV_PrimaryOwner__c = :conId
					or SCV_JointOwner__c = :conId
					ORDER BY Active_Flag__c DESC NULLS LAST
			];
		}

		return finAccs;
	}
	/**
	 * Saves multiple cases for multiple financial accounts. ESL-1138
	 * <AUTHOR> Villamin [Accenture]
	 * @created 24/09/2018
	 **/
	/**
		Edit: Vincent Inocente July 1,2019
		Added: String transfer in parameters

		Edit: burhan (Sept 23, 2019), added parameter callTypeSelect
	**/
	@AuraEnabled
	public static List<Case> saveMultipleCases(string accountId, string contactId, List<String> finAccountIds, string category, string subCategory, string classification,
			string callerType, string callerName, string idCheckResult, string description, string myAMP, string yesPlan, string applyOnline, string firstName, string lastName,
			string company, string recordTypeId, string transfer, string applyOnlineOffered, string caseType, String callTypeSelect) {
		List<Case> savedCases = new List<Case>();
		List<Id> savedCaseIds = new List<Id>();
		for (String finAcctId : finAccountIds) {
			Case c = new Case();

			c.Financial_Account__c = finAcctId;
			c.RecordTypeId = recordTypeId;

			if (accountId != null)
				c.Account_Name__c = accountId;

			if (contactId != null)
				c.Contact_Name__c = contactId;

			if (description != null)
				c.description = description;

			if (category != null)
				c.Category__c = category;

			if (subCategory != null)
				c.Sub_Category__c = subCategory;

			if (classification != null)
				c.Classification__c = classification;

			if (callerType != null)
				c.Caller_Type__c = callerType;

			if (callerName != null)
				c.Caller_Name__c = callerName;

			if (idCheckResult != null)
				c.ID_Check_Result__c = idCheckResult;

			if (firstName != null)
				c.First_Name__c = firstName;

			if (lastName != null)
				c.Last_Name__c = lastName;

			if (company != null)
				c.Company__c = company;

			c.Yes_Plan_Employed__c = boolean.valueOf(yesPlan);
			if (getCaseLocation(recordTypeId) == 'Assist') {
				c.Apply_Online_Offered__c = boolean.valueOf(applyOnlineOffered);
			} else {
				c.Apply_Online_Offered__c = boolean.valueOf(applyOnline);
			}
			c.My_AMP__c = myAMP;
			c.Transfer__c = boolean.valueOf(transfer);
			c.Type = caseType;

			//SFP-17853, burhan, commented-out for revert by SFP-21683 burhan
			//if(callTypeSelect != null){
			//	c.Call_Type_AMP_Assist_only__c = callTypeSelect;
			//}

			savedCases.add(c);

		}
		insert savedCases;
		for (Case savedC : savedCases) {
			savedCaseIds.add(savedC.Id);
		}
		return [
				select Id, CaseNumber, description, Sub_Category__c, Classification__c
				from Case
				where Id IN :savedCaseIds
		];
	}

	/**
		Edit: Vincent Inocente July 1,2019
		Added: String transfer in parameters

		Edit: burhan (Sept 23, 2019), added parameter callTypeSelect
	**/
	@AuraEnabled
	public static case saveCase(string accountId, string contactId, string finAccountId, string category, string subCategory, string classification,
			string callerType, string callerName, string idCheckResult, string description, string myAMP, string yesPlan, string applyOnline, string firstName, string lastName,
			string company, string recordTypeId, string transfer, string caseType, string applyOnlineOffered, String callTypeSelect) {

		string classOfBusiness = '';

		if (finAccountId != null && finAccountId != '') {
			classOfBusiness = [select Product__r.Class_Of_Business__c from FinServ__FinancialAccount__c where Id = :finAccountId limit 1].Product__r.Class_Of_Business__c;
		}

		Case c = new Case();

		//JD
		/*if(recordType == 'Contact Centre Service (AU)'){
			c.RecordTypeId = CONSTANTSCASES.CASE_RT_CONTACT_CENTRE_SERVICE_AU;
		}else{
				c.RecordTypeId = CONSTANTSCASES.CASE_RT_CONTACT_CENTRE_SERVICE_BANK;
		}*/
		//JD

		c.RecordTypeId = recordTypeId;

		if (accountId != null)
			c.Account_Name__c = accountId;

		if (contactId != null)
			c.Contact_Name__c = contactId;

		//Salanap (ECRM-14780) - updated
		if (finAccountId != null && finAccountId != '') {
			c.Financial_Account__c = finAccountId;
		} else {
			c.Non_product_related_Cannot_find_Contract__c = true;
		}

		if (description != null)
			c.description = description;

		if (category != null)
			c.Category__c = category;

		if (subCategory != null)
			c.Sub_Category__c = subCategory;

		if (classification != null)
			c.Classification__c = classification;

		if (callerType != null)
			c.Caller_Type__c = callerType;

		if (callerName != null)
			c.Caller_Name__c = callerName;

		if (idCheckResult != null)
			c.ID_Check_Result__c = idCheckResult;

		if (firstName != null)
			c.First_Name__c = firstName;

		if (lastName != null)
			c.Last_Name__c = lastName;

		if (company != null)
			c.Company__c = company;

		if (classOfBusiness != '' && classOfBusiness != null)
			c.Class_of_Business__c = classOfBusiness;

		c.Yes_Plan_Employed__c = boolean.valueOf(yesPlan);
		if (getCaseLocation(recordTypeId) == 'Assist') {
			c.Apply_Online_Offered__c = boolean.valueOf(applyOnlineOffered);
		} else {
			c.Apply_Online_Offered__c = boolean.valueOf(applyOnline);
		}
		c.My_AMP__c = myAMP;
		c.Transfer__c = boolean.valueOf(transfer);
		c.Type = caseType;

		//SFP-17853, burhan, commented-out for revert by SFP-21683 burhan
		//if(callTypeSelect != null){
		//	c.Call_Type_AMP_Assist_only__c = callTypeSelect;
		//}

		insert c;
		return [
				select Id, CaseNumber, description, Sub_Category__c, Classification__c
				from Case
				where Id = :c.Id
		];

	}

	@AuraEnabled
	public static List<String> getOptions(String objName, String fieldName, Id recordTypeId) {
		/*List<string> options = new List<string>();

		List<Schema.PicklistEntry> plOptions = Schema.getGlobalDescribe().get(objName).getDescribe().fields.getMap().get(fieldName).getDescribe().getPicklistValues();

		for(Schema.PicklistEntry option : plOptions){
			options.add(option.getLabel());
		}

		return options;*/
		return ContactCentreUtility.describe(objName, recordTypeId, fieldName);
	}

	@AuraEnabled
	public static List<String> getDependentOptions(String objName, String contrfieldName, String depfieldName, string contrValue) {

		String objectName = objName.toLowerCase();
		String controllingField = contrfieldName.toLowerCase();
		String dependentField = depfieldName.toLowerCase();

		Map<String, List<String>> objResults = new Map<String, List<String>>();
		//get the string to sobject global map
		Map<String, Schema.SObjectType> objGlobalMap = Schema.getGlobalDescribe();
		if (!Schema.getGlobalDescribe().containsKey(objectName)) {
			UtilityClass.debug('OBJNAME NOT FOUND --.> ' + objectName);
			return null;
		}

		Schema.SObjectType objType = Schema.getGlobalDescribe().get(objectName);
		if (objType == null) {
			return null;
		}
		Bitset bitSetObj = new Bitset();
		Map<String, Schema.SObjectField> objFieldMap = objType.getDescribe().fields.getMap();
		UtilityClass.debug('===> objFieldMap <===');
		UtilityClass.debug(objFieldMap);
		//Check if picklist values exist
		if (!objFieldMap.containsKey(controllingField) || !objFieldMap.containsKey(dependentField)) {
			UtilityClass.debug('FIELD NOT FOUND --.> ' + controllingField + ' OR ' + dependentField);
			return null;
		}
		List<Schema.PicklistEntry> contrEntries = objFieldMap.get(controllingField).getDescribe().getPicklistValues();
		List<Schema.PicklistEntry> depEntries = objFieldMap.get(dependentField).getDescribe().getPicklistValues();
		objFieldMap = null;
		List<Integer> controllingIndexes = new List<Integer>();
		for (Integer contrIndex = 0; contrIndex < contrEntries.size(); contrIndex++) {
			Schema.PicklistEntry ctrlentry = contrEntries[contrIndex];
			String label = ctrlentry.getLabel();
			objResults.put(label, new List<String>());
			controllingIndexes.add(contrIndex);
		}

		List<Schema.PicklistEntry> objEntries = new List<Schema.PicklistEntry>();
		List<PicklistEntryWrapper> objJsonEntries = new List<PicklistEntryWrapper>();
		for (Integer dependentIndex = 0; dependentIndex < depEntries.size(); dependentIndex++) {
			Schema.PicklistEntry depentry = depEntries[dependentIndex];
			objEntries.add(depentry);
		}
		objJsonEntries = (List<PicklistEntryWrapper>) JSON.deserialize(JSON.serialize(objEntries), List<PicklistEntryWrapper>.class);

		List<Integer> indexes;
		for (PicklistEntryWrapper objJson : objJsonEntries) {
			UtilityClass.debug('===> objJson.label <===');
			UtilityClass.debug(objJson.label);
			UtilityClass.debug('===> objJson.validFor <===');
			UtilityClass.debug(objJson.validFor);

			indexes = bitSetObj.testBits(objJson.validFor, controllingIndexes);
			for (Integer idx : indexes) {
				String contrLabel = contrEntries[idx].getLabel();
				objResults.get(contrLabel).add(objJson.label);
			}
		}

		UtilityClass.debug('===> objJsonEntries <===');
		UtilityClass.debug(objJsonEntries);

		UtilityClass.debug('===> objResults <===');
		UtilityClass.debug(objResults.get(contrValue));

		objEntries = null;
		objJsonEntries = null;
		return objResults.get(contrValue);
	}

	public class PicklistEntryWrapper {

		public PicklistEntryWrapper() {
		}

		public String active { get; set; }
		public String defaultValue { get; set; }
		public String label { get; set; }
		public String value { get; set; }
		public String validFor { get; set; }
	}

	public class BitSet {
		public Map<String, Integer> alphaNumCharCodes { get; set; }
		public Map<String, Integer> base64CharCodes { get; set; }

		public BitSet() {
			LoadCharCodes();
		}

		//Method loads the character codes for all letters
		private void LoadCharCodes() {
			alphaNumCharCodes = new Map<String, Integer>{
					'A' => 65, 'B' => 66, 'C' => 67, 'D' => 68, 'E' => 69, 'F' => 70, 'G' => 71, 'H' => 72, 'I' => 73, 'J' => 74,
					'K' => 75, 'L' => 76, 'M' => 77, 'N' => 78, 'O' => 79, 'P' => 80, 'Q' => 81, 'R' => 82, 'S' => 83, 'T' => 84,
					'U' => 85, 'V' => 86, 'W' => 87, 'X' => 88, 'Y' => 89, 'Z' => 90
			};
			base64CharCodes = new Map<String, Integer>();
			//all lower cases
			Set<String> pUpperCase = alphaNumCharCodes.keySet();
			for (String pKey : pUpperCase) {
				//the difference between upper case and lower case is 32
				alphaNumCharCodes.put(pKey.toLowerCase(), alphaNumCharCodes.get(pKey) + 32);
				//Base 64 alpha starts from 0 (The ascii charcodes started from 65)
				base64CharCodes.put(pKey, alphaNumCharCodes.get(pKey) - 65);
				base64CharCodes.put(pKey.toLowerCase(), alphaNumCharCodes.get(pKey) - (65) + 26);
			}
			//numerics
			for (Integer i = 0; i <= 9; i++) {
				alphaNumCharCodes.put(string.valueOf(i), i + 48);
				//base 64 numeric starts from 52
				base64CharCodes.put(string.valueOf(i), i + 52);
			}
		}

		public List<Integer> testBits(String pValidFor, List<Integer> nList) {
			List<Integer> results = new List<Integer>();
			List<Integer> pBytes = new List<Integer>();
			Integer bytesBeingUsed = (pValidFor.length() * 6) / 8;
			Integer pFullValue = 0;
			if (bytesBeingUsed <= 1)
				return results;
			for (Integer i = 0; i < pValidFor.length(); i++) {
				pBytes.Add((base64CharCodes.get((pValidFor.Substring(i, i + 1)))));
			}
			for (Integer i = 0; i < pBytes.size(); i++) {
				Integer pShiftAmount = (pBytes.size() - (i + 1)) * 6;//used to shift by a factor 6 bits to get the value
				pFullValue = pFullValue + (pBytes[i] << (pShiftAmount));
			}

			Integer bit;
			Integer targetOctet;
			Integer shiftBits;
			Integer tBitVal;
			Integer n;
			Integer nListSize = nList.size();
			for (Integer i = 0; i < nListSize; i++) {
				n = nList[i];
				bit = 7 - (Math.mod(n, 8));
				targetOctet = (bytesBeingUsed - 1) - (n >> bytesBeingUsed);
				shiftBits = (targetOctet * 8) + bit;
				tBitVal = ((Integer) (2 << (shiftBits - 1)) & pFullValue) >> shiftBits;
				if (tBitVal == 1)
					results.add(n);
			}
			return results;
		}
	}

	@AuraEnabled
	public static ContactCentreCache__c getAgentCache() {
		ContactCentreCache__c cache = ContactCentreUtility.getUserCache();
		UtilityClass.debug(cache);
		return cache;
	}

	//SFP-17853, burhan, commented-out for revert by SFP-21683 burhan
	// @AuraEnabled
	// public static CaseAssistRTResponseWrapper hasCasesAssistPermissionSet(){
	// 	List<PermissionSetAssignment> permSetList = [
	// 		SELECT PermissionSetId 
	// 		FROM PermissionSetAssignment 
	// 		WHERE PermissionSet.Name = 'Cases_Assist'
	// 		AND AssigneeId = :UserInfo.getUserId()
	// 	];

	// 	CaseAssistRTResponseWrapper responseWrapper = new CaseAssistRTResponseWrapper();

	// 	responseWrapper.hasCasesAssistPermissionSet = permSetList.size() > 0;
	// 	responseWrapper.caseAssistRTId = ConstantsCases.CASE_RT_CCS_ASSIST;

	// 	return responseWrapper;
	// }

	//SFP-17853, burhan, commented-out for revert by SFP-21683 burhan
	// public class CaseAssistRTResponseWrapper {
	// 	@AuraEnabled public Boolean hasCasesAssistPermissionSet;
	// 	@AuraEnabled public Id caseAssistRTId;
	// }

}