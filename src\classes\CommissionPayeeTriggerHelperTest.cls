/**
* @Description: Test coverage class for CommissionPayeeTriggerHelper
*               ESL-1828 - Increase test class coverage
* @Author: <PERSON> (Accenture)
*/
@IsTest(SeeAllData=false isParallel=false)
public class CommissionPayeeTriggerHelperTest {

	@testSetup
	static void setup() {
		insert TestDataBuilder.setupPracticeType();
		insert TestDataBuilder.setupHierarchySetting();
		insert TestDataBuilder.setupBypassForSysAdmin();
		TestDataBuilder.setupIntegrationCustomSettings();

		// Create Brand
		Brand__c brand1 = new Brand__c();
		brand1.Name = 'ContactHelperBRAND';
		insert brand1;

		// Create Account - LICENSEE
		Account licensee = TestDataBuilder.buildTestAccount(1, ConstantsGlobal.ACCOUNT_RT_LICENSEE_ID);
		licensee.Name = 'ContactHelperLICENSEE';
		licensee.From_Individual_Lead__c = true;
		insert licensee;

		// Create Account - PRACTICE
		Account practice = AccountTestDataFactory.generateAccount(ConstantsGlobal.ACCOUNT_RT_PRACTICE_ID);
		practice.Brand__c = brand1.Id;
		practice.Licensee__c = licensee.Id;
		insert practice;

		//Create user records
		User testUser = TestDataBuilder.buildTestUser(1);
		testUser.LastName = 'Test User';
		insert testUser;

		//Create Adviser Contact
		Contact con = TestDataBuilder.buildTestAdviserContact(testUser.Id, practice.Id);
		insert con;


		Commission_Payee__c comPayee = TestDataBuilder.buildTestCommPayee(practice.Id, '001222', 'IDMS');
		comPayee.Advised_Flag__c = True;
		comPayee.Status__c = 'Active';
		insert comPayee;

		Sales_Account__c sa = TestDataBuilder.buildTestSalesAccount(1, practice.Id, 'salesId', comPayee.Id);
		sa.Adviser__c = con.Id;
		insert sa;

	}

	static testMethod void updateSalesAccountOnCommissionPayeeChangeTest() {
		Commission_Payee__c cp = [SELECT Id, Advised_Flag__c FROM Commission_Payee__c WHERE Advised_Flag__c = True LIMIT 1];

		Test.startTest();
		cp.Advised_Flag__c = False;
		update cp;
		Test.stopTest();

		Sales_Account__c salesAcc = [SELECT Id, Advised_Flag__c, Commission_Payee__c FROM Sales_Account__c LIMIT 1];
		System.assertEquals(cp.Advised_Flag__c, salesAcc.Advised_Flag__c);


	}

	static testMethod void updateSalesAccLoadToFixed_update() {
		Commission_Payee__c cp = [SELECT Id, Advised_Flag__c, Status__c, Integration_Id__c FROM Commission_Payee__c WHERE Advised_Flag__c = True LIMIT 1];
		Contact con = [SELECT Id FROM Contact WHERE RecordTypeId =: ConstantsHierarchy.CONTACT_RT_ADVISER_ID];
		Account prac = [SELECT Id, Payee_Id__c FROM Account WHERE RecordTypeId =: ConstantsGlobal.ACCOUNT_RT_PRACTICE_ID];
		prac.Sub_Type__c = 'Derived';
		prac.Payee_Id__c = '01010';
		update prac;
		
		test.startTest();
		//create Sales Account
		Sales_Account__c sa = TestDataBuilder.buildTestSalesAccount('NewTest', prac.Id, '123456', cp.Id, con.Id, null);
		insert sa;
		// create Sales Account Load to insert
		Sales_Account_Load__c sal = TestDataBuilder.buildTestSalesAccountLoad('123456', null, cp.Integration_Id__c, 'IDMS', 'Active');
		insert sal;

		cp.Status__c = 'Inactive';
		update cp;
		test.stopTest();

		List<Sales_Account_Load__c> salList = [SELECT Id, Integration_Status__c FROM Sales_Account_Load__c WHERE Calculated_Payee_Id__c =: cp.Integration_Id__c LIMIT 1];
        System.assertEquals(salList[0].Integration_Status__c, 'Fixed');
	}
    
    static testMethod void updateSalesAccLoadToFixed_updateIntegId() {
		Commission_Payee__c cp = [SELECT Id, Advised_Flag__c, Status__c, Integration_Id__c FROM Commission_Payee__c WHERE Advised_Flag__c = True LIMIT 1];
		Contact con = [SELECT Id FROM Contact WHERE RecordTypeId =: ConstantsHierarchy.CONTACT_RT_ADVISER_ID];
		Account prac = [SELECT Id, Payee_Id__c FROM Account WHERE RecordTypeId =: ConstantsGlobal.ACCOUNT_RT_PRACTICE_ID];
		prac.Sub_Type__c = 'Derived';
		prac.Payee_Id__c = '01010';
		update prac;
		
		test.startTest();
		//create Sales Account
		Sales_Account__c sa = TestDataBuilder.buildTestSalesAccount('NewTest', prac.Id, '123456', cp.Id, con.Id, null);
		insert sa;
		// create Sales Account Load to insert
		Sales_Account_Load__c sal = TestDataBuilder.buildTestSalesAccountLoad('123456', null, '001223', 'IDMS', 'Active');
		insert sal;

		cp.Integration_Id__c = '001223';
		update cp;
		test.stopTest();

		List<Sales_Account_Load__c> salList = [SELECT Id, Integration_Status__c FROM Sales_Account_Load__c WHERE Calculated_Payee_Id__c =: '001223' LIMIT 1];
        System.assertEquals(salList[0].Integration_Status__c, 'Fixed');
	}
    
    static testMethod void updateSalesAccLoadToFixed_insert() {
		Commission_Payee__c cp = [SELECT Id, Advised_Flag__c, Status__c, Integration_Id__c FROM Commission_Payee__c WHERE Advised_Flag__c = True LIMIT 1];
		Contact con = [SELECT Id FROM Contact WHERE RecordTypeId =: ConstantsHierarchy.CONTACT_RT_ADVISER_ID];
		Account prac = [SELECT Id, Payee_Id__c FROM Account WHERE RecordTypeId =: ConstantsGlobal.ACCOUNT_RT_PRACTICE_ID];
		prac.Sub_Type__c = 'Derived';
		prac.Payee_Id__c = '01010';
		update prac;
		
		//create Sales Account
		Sales_Account__c sa = TestDataBuilder.buildTestSalesAccount('NewTest', prac.Id, '123456', cp.Id, con.Id, null);
		insert sa;
		// create Sales Account Load to insert
		Sales_Account_Load__c sal = TestDataBuilder.buildTestSalesAccountLoad('123456', null, '001223', 'IDMS', 'Active');
		insert sal;

		test.startTest();
		Commission_Payee__c comPayee = TestDataBuilder.buildTestCommPayee(prac.Id, '001223', 'IDMS');
		comPayee.Advised_Flag__c = True;
		comPayee.Status__c = 'Active';
        comPayee.Integration_Id__c = '001223';
		insert comPayee;
		test.stopTest();

		List<Sales_Account_Load__c> salList = [SELECT Id, Integration_Status__c FROM Sales_Account_Load__c WHERE Calculated_Payee_Id__c =: '001223' LIMIT 1];
        System.assertEquals(salList[0].Integration_Status__c, 'Fixed');
	}
    
    static testMethod void updateSalesAccLoadToFixed_delete() {
		Commission_Payee__c cp = [SELECT Id, Advised_Flag__c, Status__c, Integration_Id__c FROM Commission_Payee__c WHERE Advised_Flag__c = True LIMIT 1];
		Contact con = [SELECT Id FROM Contact WHERE RecordTypeId =: ConstantsHierarchy.CONTACT_RT_ADVISER_ID];
		Account prac = [SELECT Id, Payee_Id__c FROM Account WHERE RecordTypeId =: ConstantsGlobal.ACCOUNT_RT_PRACTICE_ID];
		prac.Sub_Type__c = 'Derived';
		prac.Payee_Id__c = '01010';
		update prac;

		test.startTest();
		//create Sales Account
		Sales_Account__c sa = TestDataBuilder.buildTestSalesAccount('NewTest', prac.Id, '123456', cp.Id, con.Id, null);
		insert sa;
		// create Sales Account Load to insert
		Sales_Account_Load__c sal = TestDataBuilder.buildTestSalesAccountLoad('123456', null, cp.Integration_Id__c, 'IDMS', 'Active');
		insert sal;

        try{
            delete cp;
        } catch (Exception e){
            System.assertEquals((e.getMessage()).contains(Label.Existing_Sales_Account_Deletion_Error_Message), true);
        }
		test.stopTest();
	}

	@isTest
	static void updateSalesAccLoadToFixed_SmallDataVolume_UpdatesRecordsSynchronously() {
		// Arrange
		final String testIntegrationId = '001223';
		final String testPayeeId = '01010';
		final String expectedIntegrationStatus = ConstantsHierarchy.SAL_INTEGRATION_STATUS_FIXED;
		final Integer smallRecordCount = 5;

		Commission_Payee__c commissionPayee = [SELECT Id, Integration_Id__c FROM Commission_Payee__c WHERE Advised_Flag__c = True LIMIT 1];
		Account practice = [SELECT Id FROM Account WHERE RecordTypeId = :ConstantsGlobal.ACCOUNT_RT_PRACTICE_ID LIMIT 1];
		practice.Sub_Type__c = 'Derived';
		practice.Payee_Id__c = testPayeeId;
		update practice;

		List<Sales_Account_Load__c> salesAccountLoadList = createSalesAccountLoadRecords(smallRecordCount, testIntegrationId);
		insert salesAccountLoadList;

		// Act
		Test.startTest();
		commissionPayee.Integration_Id__c = testIntegrationId;
		update commissionPayee;
		Test.stopTest();

		// Assert
		List<Sales_Account_Load__c> updatedRecords = [
			SELECT Id, Integration_Status__c
			FROM Sales_Account_Load__c
			WHERE Calculated_Payee_Id__c = :testIntegrationId
		];

		System.assertEquals(smallRecordCount, updatedRecords.size(),
			'Expected number of Sales Account Load records should be found');

		for (Sales_Account_Load__c record : updatedRecords) {
			System.assertEquals(expectedIntegrationStatus, record.Integration_Status__c,
				'Sales Account Load record should have Fixed integration status');
		}
	}

	@isTest
	static void updateSalesAccLoadToFixed_LargeDataVolume_HandlesRecordsWithoutErrors() {
		// Arrange
		final String testIntegrationId = '001224';
		final String testPayeeId = '01010';
		final Integer largeRecordCount = 200; // Simulates large volume within test limits

		Commission_Payee__c commissionPayee = [SELECT Id, Integration_Id__c FROM Commission_Payee__c WHERE Advised_Flag__c = True LIMIT 1];
		Account practice = [SELECT Id FROM Account WHERE RecordTypeId = :ConstantsGlobal.ACCOUNT_RT_PRACTICE_ID LIMIT 1];
		practice.Sub_Type__c = 'Derived';
		practice.Payee_Id__c = testPayeeId;
		update practice;

		List<Sales_Account_Load__c> salesAccountLoadList = createSalesAccountLoadRecords(largeRecordCount, testIntegrationId);
		insert salesAccountLoadList;

		// Act
		Test.startTest();
		commissionPayee.Integration_Id__c = testIntegrationId;
		update commissionPayee;
		Test.stopTest();

		// Assert
		List<Sales_Account_Load__c> existingRecords = [
			SELECT Id, Integration_Status__c
			FROM Sales_Account_Load__c
			WHERE Calculated_Payee_Id__c = :testIntegrationId
		];

		System.assertEquals(largeRecordCount, existingRecords.size(),
			'All Sales Account Load records should be found after processing');

		// Note: Actual async processing verification is handled in SalesAccountLoadUpdateQueueableTest
		// This test verifies the trigger helper method handles large volumes without throwing exceptions
	}

	@isTest
	static void updateSalesAccLoadToFixed_NullIntegrationId_HandlesGracefully() {
		// Arrange
		final String expectedClassName = 'CommissionPayeeTriggerHelper';
		List<Commission_Payee__c> commissionPayeeListWithNullId = new List<Commission_Payee__c>();
		Commission_Payee__c payeeWithNullId = new Commission_Payee__c();
		payeeWithNullId.Integration_Id__c = null;
		commissionPayeeListWithNullId.add(payeeWithNullId);

		// Act
		Test.startTest();
		Boolean exceptionThrown = false;
		try {
			CommissionPayeeTriggerHelper.updateSalesAccLoadToFixed(commissionPayeeListWithNullId, null);
		} catch (Exception e) {
			exceptionThrown = true;
		}
		Test.stopTest();

		// Assert
		System.assertEquals(false, exceptionThrown,
			'Method should handle null Integration_Id without throwing exceptions');

		// Verify error logging mechanism is available (logs may or may not be created for null handling)
		List<Application_Log__c> logs = [
			SELECT Id, Class_Name__c, Method_Name__c
			FROM Application_Log__c
			WHERE Class_Name__c = :expectedClassName
		];
		// Note: The presence of logs depends on implementation - this verifies the logging system is accessible
	}

	/**
	 * Helper method to create Sales Account Load records for testing
	 */
	private static List<Sales_Account_Load__c> createSalesAccountLoadRecords(Integer recordCount, String integrationId) {
		List<Sales_Account_Load__c> salesAccountLoadList = new List<Sales_Account_Load__c>();

		for (Integer i = 0; i < recordCount; i++) {
			Sales_Account_Load__c salesAccountLoad = TestDataBuilder.buildTestSalesAccountLoad(
				'123456' + i,
				null,
				integrationId,
				'IDMS',
				'Active'
			);
			salesAccountLoadList.add(salesAccountLoad);
		}

		return salesAccountLoadList;
	}
}